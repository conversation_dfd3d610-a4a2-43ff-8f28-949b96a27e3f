<template>
  <div :class="['w-full', isFullscreen ? 'mobile-fullscreen' : 'space-y-6']">
    <!-- Game Container -->
    <div
      :class="[
        'relative bg-black rounded-xl overflow-hidden shadow-2xl',
        isFullscreen ? 'w-full h-full' : 'game-viewport mx-auto',
      ]"
    >
      <!-- Loading Overlay -->
      <div
        v-if="loading"
        class="absolute inset-0 z-10 flex items-center justify-center bg-black/80 backdrop-blur-sm"
      >
        <div class="space-y-4 text-center">
          <div class="relative">
            <div class="mx-auto loading-spinner"></div>
            <div class="pulse-ring"></div>
            <div class="pulse-ring"></div>
            <div class="pulse-ring"></div>
          </div>
          <h3 class="text-xl font-semibold text-white">
            正在加载RetroArch模拟器...
          </h3>
          <p class="text-gray-300">正在下载 {{ core?.name }} 核心...</p>
          <div class="w-64 h-2 mx-auto bg-gray-700 rounded-full">
            <div
              class="h-2 rounded-full bg-primary-500 animate-pulse"
              style="width: 45%"
            ></div>
          </div>
        </div>
      </div>

      <!-- Game Canvas -->
      <div
        id="canvas"
        class="flex items-center justify-center w-full h-full bg-black min-h-[400px]"
        style="width: 100%; height: 100%; min-height: 400px"
      ></div>
    </div>

    <!-- Controls -->
    <div
      :class="[
        'game-controls',
        isFullscreen ? 'fixed bottom-0 left-0 right-0 z-30 rounded-none' : '',
      ]"
    >
      <button
        v-if="!loading"
        @click="startGame"
        class="flex items-center gap-2 btn-primary"
      >
        <span class="text-lg">🎮</span>
        启动游戏
      </button>

      <button
        @click="stopEmulator"
        class="flex items-center gap-2 btn-secondary"
      >
        <span class="text-lg">⏹️</span>
        停止游戏
      </button>

      <button
        @click="$emit('back')"
        class="flex items-center gap-2 btn-secondary"
      >
        <span class="text-lg">←</span>
        返回选择
      </button>

      <!-- Status Indicator -->
      <div class="flex items-center gap-2">
        <div
          :class="[
            'status-indicator',
            loading ? 'status-loading' : 'status-playing',
          ]"
        >
          <div class="w-2 h-2 mr-2 bg-current rounded-full animate-pulse"></div>
          {{ loading ? "加载中" : "运行中" }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from "vue";
import type { GameFile, EmulatorCore } from "../types/emulator";
import toast from "../utils/toast";
import {
  getBiosFile,
  getBiosFileUrl,
  getAllBiosFiles,
} from "../utils/biosStorage";
import {
  getRecommendedPresetBios,
  getRecommendedPresetBiosByCore,
  downloadAndInstallPresetBios,
  type PresetBiosItem,
} from "../utils/presetBiosManager";

const props = defineProps<{
  game: GameFile;
  core: EmulatorCore | null;
  loading: boolean;
}>();

const emit = defineEmits<{
  error: [message: string];
  stop: [];
  back: [];
}>();

const loading = ref(true);
const isFullscreen = ref(false);
const isPortrait = ref(window.innerHeight > window.innerWidth);

// BIOS 加载函数
async function loadBiosForSystem(systemType: string, gameUrl: string) {
  try {
    console.log(`🔧 检查 ${systemType} 系统的 BIOS 文件...`);

    // 对于 FBNeo 核心，确保设置不解压模式
    if (
      props.core?.id === "fbneo" ||
      props.core?.id?.includes("fbalpha") ||
      systemType === "arcade"
    ) {
      (window as any).EJS_dontExtractBIOS = true;
      console.log("🎮 FBNeo/街机模式：设置不解压 BIOS 文件");
    }

    // 从游戏文件名提取游戏名称
    const gameName = gameUrl.split("/").pop()?.split(".")[0] || "";
    console.log(`🎮 游戏名称: ${gameName}`);
    console.log(`🔍 游戏URL: ${gameUrl}`);
    console.log(`🔍 URL分割结果:`, gameUrl.split("/"));
    console.log(`🔍 文件名:`, gameUrl.split("/").pop());

    // 获取推荐的预置 BIOS - 优先使用核心 ID，然后使用游戏名称
    let recommendedBios: PresetBiosItem[] = [];

    // 首先尝试基于核心 ID 获取推荐 BIOS
    if (props.core?.id) {
      recommendedBios = getRecommendedPresetBiosByCore(props.core.id);
      console.log(
        `💡 基于核心 ${props.core.id} 推荐的 BIOS:`,
        recommendedBios.map((b) => b.name)
      );
    }

    // 如果基于核心没有找到，再尝试基于游戏名称
    if (recommendedBios.length === 0) {
      recommendedBios = getRecommendedPresetBios(gameName);
      console.log(
        `💡 基于游戏名称 ${gameName} 推荐的 BIOS:`,
        recommendedBios.map((b) => b.name)
      );
    }

    console.log(`💡 最终推荐的 BIOS 数量: ${recommendedBios.length}`);

    // 调试：查看数据库中的所有 BIOS 文件
    try {
      const allBiosFiles = await getAllBiosFiles();
      console.log(
        `🗄️ 数据库中的所有 BIOS 文件:`,
        allBiosFiles.map((f) => f.name)
      );
    } catch (error) {
      console.error("获取 BIOS 文件列表失败:", error);
    }

    // 测试特定游戏名称
    if (gameName.toLowerCase().includes("kof")) {
      console.log(`🔍 检测到 KOF 游戏，测试推荐算法:`);
      const testRecommended = getRecommendedPresetBios("kof97");
      console.log(
        `🔍 kof97 推荐结果:`,
        testRecommended.map((b) => b.name)
      );
    }

    // 尝试加载推荐的 BIOS 文件
    for (const bios of recommendedBios) {
      console.log(`🔧 尝试加载 BIOS 文件: ${bios.filename}`);

      console.log(`🔍 查找 BIOS 文件: ${bios.filename}`);
      const biosFile = await getBiosFile(bios.filename);
      console.log(`🔍 BIOS 文件查找结果:`, biosFile ? "找到" : "未找到");
      if (biosFile) {
        const biosUrl = await getBiosFileUrl(bios.filename);
        if (biosUrl) {
          // 对于 FBNeo 核心，需要特殊处理
          if (
            props.core?.id === "fbneo" ||
            props.core?.id?.includes("fbalpha")
          ) {
            (window as any).EJS_dontExtractBIOS = true;
            (window as any).EJS_biosUrl = biosUrl;
            console.log(
              `✅ FBNeo BIOS 文件 ${bios.filename} 已加载 (不解压模式)`
            );
            console.log(
              `🔧 EJS_dontExtractBIOS = ${(window as any).EJS_dontExtractBIOS}`
            );
            console.log(`🔧 EJS_biosUrl = ${(window as any).EJS_biosUrl}`);
          } else {
            (window as any).EJS_biosUrl = biosUrl;
            console.log(`✅ BIOS 文件 ${bios.filename} 已加载`);
          }
          return; // 找到并加载了 BIOS，退出
        }
      } else {
        console.warn(`⚠️ 未找到 BIOS 文件: ${bios.filename}`);

        // 如果是可下载的预置 BIOS，自动下载
        if (bios.url && bios.isVerified) {
          console.log(`🔄 自动下载 BIOS: ${bios.name}`);

          // 显示下载提示
          toast.info(`检测到游戏需要 ${bios.name}，正在自动下载...`, {
            duration: 3000,
          });

          try {
            // 自动下载并安装 BIOS
            await downloadAndInstallPresetBios(bios.id);

            // 下载成功后重新尝试加载
            const biosFile = await getBiosFile(bios.filename);
            if (biosFile) {
              const biosUrl = await getBiosFileUrl(bios.filename);
              if (biosUrl) {
                // 对于 FBNeo 核心，需要特殊处理
                if (
                  props.core?.id === "fbneo" ||
                  props.core?.id?.includes("fbalpha")
                ) {
                  (window as any).EJS_dontExtractBIOS = true;
                  (window as any).EJS_biosUrl = biosUrl;
                  console.log(
                    `✅ FBNeo BIOS 文件 ${bios.filename} 下载并加载成功 (不解压模式)`
                  );
                } else {
                  (window as any).EJS_biosUrl = biosUrl;
                  console.log(`✅ BIOS 文件 ${bios.filename} 下载并加载成功`);
                }

                toast.success(`${bios.name} 下载成功！游戏即将启动`, {
                  duration: 3000,
                });

                return; // 成功下载并加载，退出
              }
            }
          } catch (error) {
            console.error(`下载 BIOS 失败:`, error);
            toast.error(`下载 ${bios.name} 失败，请手动在 BIOS 管理页面下载`, {
              duration: 5000,
            });
          }
        } else if (bios.url) {
          // 如果有 URL 但未验证，提示用户手动下载
          toast.warning(`游戏需要 ${bios.name}，请在 BIOS 管理页面下载`, {
            duration: 5000,
          });
        } else {
          // 没有下载链接，提示用户手动上传
          toast.warning(`游戏需要 ${bios.name}，请在 BIOS 管理页面手动上传`, {
            duration: 5000,
          });
        }
      }
    }

    // 如果没有找到推荐的 BIOS，使用传统的检测方法
    if (recommendedBios.length === 0) {
      let biosFileName = "";

      // 街机游戏 BIOS 检测
      if (
        systemType === "arcade" ||
        systemType === "fbneo" ||
        systemType === "fbalpha"
      ) {
        const lowerGameUrl = gameUrl.toLowerCase();

        // Neo Geo 游戏检测
        if (
          lowerGameUrl.includes("kof") ||
          lowerGameUrl.includes("neogeo") ||
          lowerGameUrl.includes("metal") ||
          lowerGameUrl.includes("samurai") ||
          lowerGameUrl.includes("garou") ||
          lowerGameUrl.includes("lastblade") ||
          lowerGameUrl.includes("fatfury")
        ) {
          biosFileName = "neogeo.zip";
        }
        // PGM 游戏检测
        else if (
          lowerGameUrl.includes("pgm") ||
          lowerGameUrl.includes("knights") ||
          lowerGameUrl.includes("ddp") ||
          lowerGameUrl.includes("kov") ||
          lowerGameUrl.includes("三国")
        ) {
          biosFileName = "pgm.zip";
        }
        // Naomi 游戏检测
        else if (
          lowerGameUrl.includes("naomi") ||
          lowerGameUrl.includes("crazy") ||
          lowerGameUrl.includes("taxi") ||
          lowerGameUrl.includes("virtua")
        ) {
          biosFileName = "naomi.zip";
        }
        // Atomiswave 游戏检测
        else if (
          lowerGameUrl.includes("awbios") ||
          lowerGameUrl.includes("atomiswave")
        ) {
          biosFileName = "awbios.zip";
        }
      }
      // PlayStation 游戏检测
      else if (systemType === "psx" || systemType === "playstation") {
        biosFileName = "scph1001.bin"; // 默认使用美版 BIOS
      }
      // Dreamcast 游戏检测
      else if (systemType === "dreamcast") {
        biosFileName = "dc_boot.bin";
      }

      if (biosFileName) {
        console.log(`🔧 传统方式检测到需要 BIOS: ${biosFileName}`);

        const biosFile = await getBiosFile(biosFileName);
        if (biosFile) {
          const biosUrl = await getBiosFileUrl(biosFileName);
          if (biosUrl) {
            // 对于 FBNeo 核心，需要特殊处理
            if (
              props.core?.id === "fbneo" ||
              props.core?.id?.includes("fbalpha")
            ) {
              (window as any).EJS_dontExtractBIOS = true;
              (window as any).EJS_biosUrl = biosUrl;
              console.log(
                `✅ FBNeo BIOS 文件 ${biosFileName} 已加载 (不解压模式)`
              );
            } else {
              (window as any).EJS_biosUrl = biosUrl;
              console.log(`✅ BIOS 文件 ${biosFileName} 已加载`);
            }
          }
        } else {
          console.warn(`⚠️ 未找到 BIOS 文件: ${biosFileName}`);

          // 尝试从预置 BIOS 中找到对应的文件并自动下载
          const allPresetBios = await import("../config/presetBios.json");
          const matchingBios = allPresetBios.default.bios.find(
            (bios: any) => bios.filename === biosFileName
          );

          if (matchingBios && matchingBios.url && matchingBios.isVerified) {
            console.log(`🔄 尝试自动下载传统检测的 BIOS: ${matchingBios.name}`);

            toast.info(`检测到游戏需要 ${matchingBios.name}，正在自动下载...`, {
              duration: 3000,
            });

            try {
              await downloadAndInstallPresetBios(matchingBios.id);

              // 下载成功后重新尝试加载
              const biosFile = await getBiosFile(biosFileName);
              if (biosFile) {
                const biosUrl = await getBiosFileUrl(biosFileName);
                if (biosUrl) {
                  // 对于 FBNeo 核心，需要特殊处理
                  if (
                    props.core?.id === "fbneo" ||
                    props.core?.id?.includes("fbalpha")
                  ) {
                    (window as any).EJS_dontExtractBIOS = true;
                    (window as any).EJS_biosUrl = biosUrl;
                    console.log(
                      `✅ FBNeo BIOS 文件 ${biosFileName} 下载并加载成功 (不解压模式)`
                    );
                  } else {
                    (window as any).EJS_biosUrl = biosUrl;
                    console.log(`✅ BIOS 文件 ${biosFileName} 下载并加载成功`);
                  }

                  toast.success(`${matchingBios.name} 下载成功！游戏即将启动`, {
                    duration: 3000,
                  });

                  return;
                }
              }
            } catch (error) {
              console.error(`下载 BIOS 失败:`, error);
              toast.error(
                `下载 ${matchingBios.name} 失败，请手动在 BIOS 管理页面下载`,
                {
                  duration: 5000,
                }
              );
            }
          } else {
            toast.warning(
              `游戏需要 ${biosFileName}，请在 BIOS 管理页面上传或下载`,
              {
                duration: 5000,
              }
            );
          }
        }
      }
    }
  } catch (error) {
    console.error("加载 BIOS 文件失败:", error);
  }
}

// 监听屏幕方向变化
function handleOrientationChange() {
  isPortrait.value = window.innerHeight > window.innerWidth;
  console.log("屏幕方向变化:", isPortrait.value ? "竖屏" : "横屏");
}

onMounted(() => {
  // 添加屏幕方向变化监听器
  window.addEventListener("resize", handleOrientationChange);
  window.addEventListener("orientationchange", handleOrientationChange);
});

// 监听 props 变化，当游戏和核心都准备好时初始化
watch(
  () => [props.game, props.core],
  async ([game, core]) => {
    if (game && core) {
      console.log("游戏和核心都准备好了，开始初始化...", {
        game: game.name,
        core: core.name,
      });
      try {
        await initializeRetroArch();
      } catch (error) {
        console.error("RetroArch初始化失败:", error);
        emit("error", "RetroArch初始化失败: " + (error as Error).message);
      }
    }
  },
  { immediate: true }
);

async function initializeRetroArch() {
  try {
    loading.value = true;

    console.log("开始加载EmulatorJS模拟器...");

    // 设置EmulatorJS配置
    const gameUrl = await createGameBlob();
    const coreType = getEmulatorJSCore(props.core?.id || "gambatte");
    const systemType = getSystemFromCore(props.core?.id || "gambatte");

    console.log("游戏URL:", gameUrl);
    console.log("核心类型:", coreType);
    console.log("系统类型:", systemType);

    // 设置回调函数（必须在加载器之前设置）
    (window as any).EJS_ready = () => {
      console.log("🎮 EmulatorJS ready!");
      loading.value = false;
    };

    (window as any).EJS_onGameStart = () => {
      console.log("🎮 Game started!");
      loading.value = false;
    };

    (window as any).EJS_onLoadState = () => {
      console.log("🎮 Game loaded!");
    };

    // 配置EmulatorJS全局变量
    (window as any).EJS_player = "#canvas";

    // 对于街机游戏，强制使用fbneo核心
    if (systemType === "arcade") {
      (window as any).EJS_core = "fbneo";
    } else {
      (window as any).EJS_core = coreType;
    }
    (window as any).EJS_system = systemType;
    (window as any).EJS_pathtodata = "/emulatorjs/";
    (window as any).EJS_gameUrl = gameUrl;

    // 对于FBNeo核心，使用标准化的游戏名称
    let gameName = props.game.name;
    if (coreType === "fbneo") {
      // 移除文件扩展名，FBNeo使用ROM名称而不是文件名
      gameName = props.game.name.replace(/\.(zip|neo|mvs)$/i, "");
      // 如果是拳皇97，尝试使用标准ROM名称
      if (gameName.toLowerCase().includes("kof") && gameName.includes("97")) {
        gameName = "kof97";
      }
    }
    (window as any).EJS_gameName = gameName;
    (window as any).EJS_startOnLoaded = true;
    (window as any).EJS_threads = false;
    (window as any).EJS_volume = 0.8;
    (window as any).EJS_forceLegacyCores = false;

    // 强制启用WebGL2以避免legacy核心
    if (systemType === "arcade") {
      (window as any).EJS_webgl2 = true;
    }

    // 街机核心特殊配置
    if (coreType === "fbneo") {
      (window as any).EJS_biosUrl = "";
      (window as any).EJS_gameParentUrl = "";
      (window as any).EJS_softLoad = 0;
    }

    console.log("EmulatorJS配置:", {
      player: (window as any).EJS_player,
      core: (window as any).EJS_core,
      system: (window as any).EJS_system,
      pathtodata: (window as any).EJS_pathtodata,
      gameUrl: (window as any).EJS_gameUrl,
      gameName: (window as any).EJS_gameName,
      startOnLoaded: (window as any).EJS_startOnLoaded,
    });

    // 验证游戏 URL 是否可访问
    console.log("验证游戏 URL:", gameUrl);
    fetch(gameUrl)
      .then((response) => {
        console.log(
          "游戏文件响应:",
          response.status,
          response.headers.get("content-type")
        );
        return response.arrayBuffer();
      })
      .then((buffer) => {
        console.log("游戏文件大小:", buffer.byteLength, "字节");
      })
      .catch((error) => {
        console.error("游戏文件访问失败:", error);
      });

    // 动态加载EmulatorJS加载器
    await loadEmulatorJS();

    // 强制修改核心URL以避免legacy版本
    if (coreType === "fbneo") {
      // 等待EmulatorJS初始化后修改其内部逻辑
      setTimeout(() => {
        if ((window as any).EJS_emulator) {
          const emulator = (window as any).EJS_emulator;
          // 强制设置WebGL2支持
          if (emulator.supportsWebgl2 !== undefined) {
            emulator.webgl2Enabled = true;
            emulator.supportsWebgl2 = true;
          }
          console.log("强制启用WebGL2:", emulator.webgl2Enabled);
        }
      }, 100);
    }
    // 给EmulatorJS一些时间来初始化
    setTimeout(() => {
      if (loading.value) {
        console.log("EmulatorJS初始化超时，尝试手动启动...");
        loading.value = false;
      }
    }, 15000); // 增加到15秒超时
  } catch (error) {
    loading.value = false;
    throw error;
  }
}

function getEmulatorJSCore(coreId: string): string {
  // EmulatorJS核心名称映射 - 直接返回核心名称
  const coreMap: { [key: string]: string } = {
    gambatte: "gambatte",
    mgba: "mgba",
    snes9x: "snes9x",
    fceumm: "fceumm",
    genesis_plus_gx: "genesis_plus_gx",
    melonds: "melonds",
    desmume: "desmume",
    fbneo: "fbneo",
  };

  return coreMap[coreId] || "gambatte";
}

function getSystemFromCore(coreId: string): string {
  const systemMap: { [key: string]: string } = {
    gambatte: "gb",
    mgba: "gba",
    snes9x: "snes",
    fceumm: "nes",
    genesis_plus_gx: "segaMD",
    melonds: "nds",
    desmume: "nds",
    fbneo: "arcade",
  };

  return systemMap[coreId] || "gb";
}

async function createGameBlob(): Promise<string> {
  try {
    console.log("原始游戏数据:", props.game.data);
    console.log("游戏数据类型:", typeof props.game.data);
    console.log("游戏数据是否为数组:", Array.isArray(props.game.data));

    // 将游戏数据转换为Blob URL
    const gameData = new Uint8Array(props.game.data);
    console.log("游戏数据大小:", gameData.length, "字节");

    // 根据文件扩展名设置正确的MIME类型
    const extension = props.game.name.split(".").pop()?.toLowerCase();
    let mimeType = "application/octet-stream";

    if (extension === "zip") {
      mimeType = "application/zip";
    } else if (extension === "gb" || extension === "gbc") {
      mimeType = "application/x-gameboy-rom";
    } else if (extension === "gba") {
      mimeType = "application/x-gba-rom";
    }

    const blob = new Blob([gameData], { type: mimeType });
    const url = URL.createObjectURL(blob);

    console.log("创建Blob URL:", url);
    console.log("MIME类型:", mimeType);

    return url;
  } catch (error) {
    console.error("创建游戏Blob失败:", error);
    throw new Error("无法创建游戏文件");
  }
}

async function loadEmulatorJS() {
  return new Promise<void>(async (resolve, reject) => {
    // 清理之前的实例
    if ((window as any).EJS_emulator) {
      try {
        (window as any).EJS_emulator.destroy();
      } catch (e) {
        console.log("清理旧实例失败:", e);
      }
      delete (window as any).EJS_emulator;
    }

    // 保存当前配置
    const gameUrl = (window as any).EJS_gameUrl;
    const coreType = (window as any).EJS_core;
    const systemType = (window as any).EJS_system;

    // 重新设置配置
    (window as any).EJS_player = "#canvas";
    (window as any).EJS_core = coreType;
    (window as any).EJS_system = systemType;
    (window as any).EJS_pathtodata = "/emulatorjs/";
    (window as any).EJS_gameUrl = gameUrl;
    (window as any).EJS_startOnLoaded = false;
    (window as any).EJS_threads = false;
    (window as any).EJS_volume = 0.8;
    (window as any).EJS_DEBUG_XX = false; // 强制使用非压缩文件，加载所有scripts数组

    // // 语言和界面设置
    // (window as any).EJS_language = "en-US"; // 使用英文避免翻译问题
    // (window as any).EJS_color = "#667eea";
    // (window as any).EJS_backgroundColor = "#222";

    // // 音频设置
    // (window as any).EJS_mute = false;
    // (window as any).EJS_defaultControls = true;

    // 对于街机游戏，不要解压ZIP文件
    if (
      systemType === "arcade" ||
      coreType === "fbalpha" ||
      coreType === "fbneo"
    ) {
      (window as any).EJS_dontExtractBIOS = true;
      console.log("🎮 街机游戏模式：ZIP文件将直接传递给核心");

      // 检测和加载 BIOS 文件
      await loadBiosForSystem(systemType, gameUrl);
    }

    // 错误处理回调
    (window as any).EJS_onGameStart = () => {
      console.log("🎮 游戏开始启动");
    };

    (window as any).EJS_onLoadState = () => {
      console.log("🎮 游戏状态加载完成");
    };

    // 添加错误处理
    (window as any).EJS_onError = (error: any) => {
      console.error("🎮 EmulatorJS 错误:", error);
    };

    // 检查是否已经加载了脚本
    const existingScript = document.querySelector(
      'script[src="/emulatorjs/loader.js"]'
    );
    if (existingScript) {
      existingScript.remove();
    }

    const script = document.createElement("script");
    script.src = "/emulatorjs/loader.js";
    script.async = true;

    script.onload = () => {
      console.log("✅ EmulatorJS加载器脚本加载成功");
      // 给脚本一些时间来初始化
      setTimeout(() => {
        if ((window as any).EJS_emulator) {
          console.log("✅ EmulatorJS初始化成功");

          // 添加事件监听器
          console.log("🎮 模拟器准备就绪");
          // 尝试自动启动游戏
          setTimeout(() => {
            try {
              console.log("尝试自动启动游戏...");
              // 查找并点击开始按钮
              const startButton = document.querySelector(".ejs_start_button");
              if (startButton) {
                console.log("找到开始按钮，自动点击...");
                (startButton as HTMLElement).click();
              } else {
                console.log("未找到开始按钮");
              }
            } catch (error) {
              console.log("自动启动游戏失败:", error);
            }
          }, 100);

          (window as any).EJS_emulator.on("start", () => {
            console.log("🎮 游戏开始");
            loading.value = false;
          });

          (window as any).EJS_emulator.on("error", (error: any) => {
            console.error("❌ EmulatorJS错误:", error);
            loading.value = false;
            emit("error", `模拟器错误: ${error}`);
          });

          resolve();
        } else {
          console.log("等待EmulatorJS初始化...");
          resolve(); // 即使没有立即初始化也继续
        }
      }, 300); // 增加到3秒等待时间
    };

    script.onerror = (error) => {
      console.error("❌ EmulatorJS加载器脚本加载失败:", error);
      reject(new Error("无法加载EmulatorJS加载器"));
    };

    document.head.appendChild(script);
  });
}

function startGame() {
  try {
    if ((window as any).EJS_emulator) {
      console.log("手动启动游戏...");
      // 尝试不同的启动方法
      if ((window as any).EJS_emulator.startGame) {
        (window as any).EJS_emulator.startGame();
      } else if (
        (window as any).EJS_emulator.gameManager &&
        (window as any).EJS_emulator.gameManager.start
      ) {
        (window as any).EJS_emulator.gameManager.start();
      } else {
        // 模拟点击开始按钮
        const startButton = document.querySelector(".ejs_start_button");
        if (startButton) {
          console.log("点击开始按钮...");
          (startButton as HTMLElement).click();
        } else {
          console.log("未找到开始按钮");
        }
      }
    } else {
      console.log("模拟器未准备就绪");
    }
  } catch (error) {
    console.error("启动游戏失败:", error);
  }
}

function stopEmulator() {
  try {
    console.log("🛑 开始停止模拟器...");

    // 首先暂停游戏
    if ((window as any).EJS_emulator) {
      try {
        // 尝试暂停游戏
        if ((window as any).EJS_emulator.pause) {
          (window as any).EJS_emulator.pause();
        }

        // 停止主循环
        if (
          (window as any).EJS_emulator.gameManager &&
          (window as any).EJS_emulator.gameManager.toggleMainLoop
        ) {
          (window as any).EJS_emulator.gameManager.toggleMainLoop(0);
        }

        // 停止音频上下文
        if (
          (window as any).EJS_emulator.Module &&
          (window as any).EJS_emulator.Module.AL
        ) {
          const alContext = (window as any).EJS_emulator.Module.AL.currentCtx;
          if (alContext && alContext.audioCtx) {
            console.log("🔇 停止音频上下文...");

            // 停止所有音频源
            if (alContext.sources) {
              Object.values(alContext.sources).forEach((source: any) => {
                try {
                  if (source.gain) {
                    source.gain.disconnect();
                  }
                  if (source.panner) {
                    source.panner.disconnect();
                  }
                } catch (e) {
                  console.log("停止音频源时出错:", e);
                }
              });
            }

            // 关闭音频上下文
            try {
              if (alContext.audioCtx.state !== "closed") {
                alContext.audioCtx.close();
              }
            } catch (e) {
              console.log("关闭音频上下文时出错:", e);
            }
          }
        }

        // 销毁模拟器实例
        (window as any).EJS_emulator.destroy();
      } catch (e) {
        console.log("销毁模拟器实例时出错:", e);
      }
      delete (window as any).EJS_emulator;
    }

    // 清理画布
    const canvas = document.querySelector("#canvas");
    if (canvas) {
      canvas.innerHTML = "";
    }

    // 清理所有音频元素
    const audioElements = document.querySelectorAll("audio");
    audioElements.forEach((audio) => {
      try {
        audio.pause();
        audio.currentTime = 0;
        audio.src = "";
        audio.load();
      } catch (e) {
        console.log("清理音频元素时出错:", e);
      }
    });

    console.log("✅ 模拟器已完全停止");
  } catch (error) {
    console.error("停止模拟器时出错:", error);
  }
  emit("stop");
}

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener("resize", handleOrientationChange);
  window.removeEventListener("orientationchange", handleOrientationChange);

  stopEmulator();
});
</script>
